<?php

namespace App\Imports;

use App\Models\Cargo;
use App\Models\Driver;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\Log;

class DriverImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            try {
                // Skip if DNI already exists (based on user preference)
                if (Driver::where('dni', $row['DNI'])->exists()) {
                    Log::info("Skipping driver with DNI {$row['DNI']} - already exists");
                    continue;
                }

                // Find cargo by name, skip if not found
                $cargo = Cargo::where('name', $row['CARGO'])->first();
                if (!$cargo) {
                    Log::warning("Cargo '{$row['CARGO']}' not found for driver {$row['NOMBRES']} {$row['APELLIDO PATERNO']}");
                    continue;
                }

                Driver::create([
                    'last_paternal_name' => $row['APELLIDO PATERNO'], // Fixed typo
                    'last_maternal_name' => $row['APELLIDO MATERNO'],
                    'name' => $row['NOMBRES'],
                    'dni' => $row['DNI'],
                    'cargo_id' => $cargo->id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);

                Log::info("Successfully imported driver: {$row['NOMBRES']} {$row['APELLIDO PATERNO']}");
            } catch (\Exception $e) {
                Log::error("Error importing driver row: " . $e->getMessage(), [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }
    }
}
