<?php

namespace App\Imports;

use App\Models\Cargo;
use App\Models\Driver;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class DriverImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {

        foreach ($rows as $row) {
            dd($row);
            $cargo_id = Cargo::where('name', $row['CARGO'])->first()->id;
            Driver::create([
                'last_paternal_name' => $row['APELIDO PATERNO'],
                'last_maternal_name' => $row['APELLIDO MATERNO'],
                'name' => $row['NOMBRES'],
                'dni' => $row['DNI'],
                'cargo_id' => $cargo_id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}
