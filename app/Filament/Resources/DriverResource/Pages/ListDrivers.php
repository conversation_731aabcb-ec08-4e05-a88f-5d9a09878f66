<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Actions;
use App\Imports\DriverImport;
use App\Filament\Imports\DriverImporter;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\DriverResource;
use Illuminate\Support\Facades\Artisan;

class ListDrivers extends ListRecords
{
    protected static string $resource = DriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ImportAction::make()
            //     ->importer(DriverImporter::class)
            //     ->icon('heroicon-o-arrow-up-tray'),
            \EightyNine\ExcelImport\ExcelImportAction::make()
                ->use(DriverImport::class)
                ->after(function () {
                    // Execute queue:work automatically after import (based on user preference)
                    try {
                        Artisan::call('queue:work', [
                            '--stop-when-empty' => true,
                            '--timeout' => 60,
                            '--memory' => 512,
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Importación completada')
                            ->body('Los datos han sido importados y procesados correctamente.')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        \Filament\Notifications\Notification::make()
                            ->title('Error en el procesamiento')
                            ->body('La importación se completó pero hubo un error en el procesamiento: ' . $e->getMessage())
                            ->warning()
                            ->send();
                    }
                }),
            Actions\CreateAction::make()
                ->icon('heroicon-o-squares-plus'),

        ];
    }
}
